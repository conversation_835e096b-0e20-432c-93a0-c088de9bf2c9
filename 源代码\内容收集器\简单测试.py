# -*- coding: utf-8 -*-
"""
简单网络测试

作者: AI助手
日期: 2025-07-29
功能: 测试网络访问和基本功能
"""

import requests
from datetime import datetime

def 测试网络访问():
    """测试基本网络访问"""
    print("🌐 测试网络访问")
    print("=" * 30)
    
    测试网站 = [
        "https://www.baidu.com",
        "https://xueqiu.com",
        "https://www.jisilu.cn",
    ]
    
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
    }
    
    for 网站 in 测试网站:
        try:
            print(f"🔗 访问: {网站}")
            response = requests.get(网站, headers=headers, timeout=10)
            print(f"   状态码: {response.status_code}")
            print(f"   响应长度: {len(response.text)} 字符")
            
            if response.status_code == 200:
                print("   ✅ 访问成功")
            else:
                print("   ❌ 访问失败")
                
        except Exception as e:
            print(f"   ❌ 访问异常: {e}")
        
        print()

def 测试雪球简单访问():
    """测试雪球简单访问"""
    print("⚪ 测试雪球访问")
    print("=" * 30)
    
    用户ID = "1314783718"
    主页URL = f"https://xueqiu.com/u/{用户ID}"
    
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        'Connection': 'keep-alive',
    }
    
    try:
        print(f"🔗 访问雪球用户页面: {主页URL}")
        response = requests.get(主页URL, headers=headers, timeout=30)
        
        print(f"状态码: {response.status_code}")
        print(f"响应长度: {len(response.text)} 字符")
        print(f"Content-Type: {response.headers.get('content-type', '未知')}")
        
        if response.status_code == 200:
            print("✅ 雪球主页访问成功")
            
            # 查看页面内容片段
            if "饕餮海" in response.text:
                print("🎯 页面包含'饕餮海'关键词")
            else:
                print("⚪ 页面不包含'饕餮海'关键词")
            
            # 查看前1000字符
            print(f"\n📄 页面内容前1000字符:")
            print(response.text[:1000])
            
        else:
            print("❌ 雪球主页访问失败")
            print(f"响应内容: {response.text[:500]}")
            
    except Exception as e:
        print(f"❌ 访问异常: {e}")

def 测试时间功能():
    """测试时间相关功能"""
    print("🕒 测试时间功能")
    print("=" * 30)
    
    # 测试时间戳转换
    测试时间戳 = [
        1722268800000,  # 2024年的某个时间戳
        int(datetime.now().timestamp() * 1000),  # 当前时间戳
        int((datetime.now().timestamp() - 86400) * 1000),  # 昨天
    ]
    
    for 时间戳 in 测试时间戳:
        try:
            日期时间 = datetime.fromtimestamp(时间戳 / 1000)
            格式化时间 = 日期时间.strftime('%Y-%m-%d %H:%M')
            print(f"时间戳 {时间戳} -> {格式化时间}")
        except Exception as e:
            print(f"时间戳转换失败: {e}")

def 主函数():
    """主函数"""
    print("🚀 简单功能测试")
    print("=" * 50)
    
    # 测试网络访问
    测试网络访问()
    
    # 测试雪球访问
    测试雪球简单访问()
    
    # 测试时间功能
    测试时间功能()
    
    print("🎉 测试完成！")

if __name__ == "__main__":
    主函数()
