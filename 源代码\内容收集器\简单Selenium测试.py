# -*- coding: utf-8 -*-
"""
简单Selenium测试

作者: AI助手
日期: 2025-07-29
功能: 测试Selenium基本功能和搜狗微信搜索
"""

import time
from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.options import Options
from webdriver_manager.chrome import ChromeDriverManager


def 测试基本功能():
    """测试Selenium基本功能"""
    print("🧪 测试Selenium基本功能")
    print("=" * 40)
    
    driver = None
    try:
        # 配置Chrome选项
        options = Options()
        options.add_argument('--disable-blink-features=AutomationControlled')
        options.add_experimental_option("excludeSwitches", ["enable-automation"])
        options.add_experimental_option('useAutomationExtension', False)
        
        print("🚀 启动Chrome浏览器...")
        service = Service(ChromeDriverManager().install())
        driver = webdriver.Chrome(service=service, options=options)
        driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
        
        print("🌐 访问百度首页...")
        driver.get("https://www.baidu.com")
        
        # 获取页面标题
        title = driver.title
        print(f"✅ 页面标题: {title}")
        
        # 查找搜索框
        search_box = driver.find_element(By.ID, "kw")
        print("✅ 找到搜索框")
        
        # 输入搜索内容
        search_box.send_keys("微信公众号")
        print("✅ 输入搜索内容")
        
        # 点击搜索按钮
        search_btn = driver.find_element(By.ID, "su")
        search_btn.click()
        print("✅ 点击搜索按钮")
        
        # 等待结果加载
        time.sleep(3)
        
        # 获取搜索结果
        results = driver.find_elements(By.CSS_SELECTOR, "h3 a")
        print(f"✅ 找到 {len(results)} 个搜索结果")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False
    finally:
        if driver:
            driver.quit()
            print("🔚 浏览器已关闭")


def 测试搜狗微信访问():
    """测试搜狗微信网站访问"""
    print("\n🧪 测试搜狗微信网站访问")
    print("=" * 40)
    
    driver = None
    try:
        # 配置Chrome选项
        options = Options()
        options.add_argument('--disable-blink-features=AutomationControlled')
        options.add_experimental_option("excludeSwitches", ["enable-automation"])
        options.add_experimental_option('useAutomationExtension', False)
        
        print("🚀 启动Chrome浏览器...")
        service = Service(ChromeDriverManager().install())
        driver = webdriver.Chrome(service=service, options=options)
        driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
        
        print("🌐 访问搜狗微信搜索...")
        driver.get("https://weixin.sogou.com/")
        
        # 等待页面加载
        time.sleep(3)
        
        # 获取页面标题
        title = driver.title
        print(f"✅ 页面标题: {title}")
        
        # 查找搜索框
        try:
            search_box = driver.find_element(By.CLASS_NAME, "sec-input")
            print("✅ 找到搜索框")
        except:
            print("❌ 未找到搜索框")
            return False
        
        # 输入搜索内容
        search_box.clear()
        search_box.send_keys("饕餮海投资")
        print("✅ 输入搜索内容: 饕餮海投资")
        
        # 查找搜文章按钮
        try:
            article_btn = driver.find_element(By.CLASS_NAME, "enter-input.article")
            print("✅ 找到搜文章按钮")
        except:
            print("❌ 未找到搜文章按钮")
            return False
        
        # 点击搜文章按钮
        article_btn.click()
        print("✅ 点击搜文章按钮")
        
        # 等待搜索结果
        time.sleep(5)
        
        # 检查是否有验证码
        try:
            captcha = driver.find_elements(By.CLASS_NAME, "p4")
            if captcha:
                print("🔐 检测到验证码页面")
                print("💡 这是正常现象，实际使用时需要手动处理")
            else:
                print("✅ 未检测到验证码")
        except:
            pass
        
        # 获取当前URL
        current_url = driver.current_url
        print(f"📍 当前URL: {current_url}")
        
        # 检查页面内容
        page_source = driver.page_source
        if "饕餮海投资" in page_source:
            print("✅ 页面包含搜索关键词")
        else:
            print("⚠️ 页面不包含搜索关键词（可能需要验证码）")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False
    finally:
        if driver:
            driver.quit()
            print("🔚 浏览器已关闭")


def 主函数():
    """主函数"""
    print("🚀 Selenium功能测试")
    print("=" * 60)
    
    测试结果 = []
    
    # 测试1: 基本功能
    结果1 = 测试基本功能()
    测试结果.append(("基本功能", 结果1))
    
    # 测试2: 搜狗微信访问
    if 结果1:  # 只有基本功能正常才继续
        结果2 = 测试搜狗微信访问()
        测试结果.append(("搜狗微信访问", 结果2))
    
    # 显示测试总结
    print(f"\n🎉 测试总结")
    print("=" * 60)
    
    成功数量 = 0
    for 测试名称, 结果 in 测试结果:
        状态 = "✅ 通过" if 结果 else "❌ 失败"
        print(f"{测试名称:15} -> {状态}")
        if 结果:
            成功数量 += 1
    
    print(f"\n📊 总体结果: {成功数量}/{len(测试结果)} 测试通过")
    
    if 成功数量 == len(测试结果):
        print("🎉 所有测试通过！Selenium环境配置正确")
        print("💡 现在可以运行完整的爬虫程序了")
    elif 成功数量 > 0:
        print("⚠️ 部分测试通过，基本功能可用")
        print("💡 搜狗微信可能需要处理验证码")
    else:
        print("❌ 所有测试失败，请检查环境配置")
        print("💡 请确保Chrome浏览器已安装")
    
    print(f"\n📖 使用说明:")
    print(f"   1. 如果测试通过，可以运行: python 简化文章采集器.py")
    print(f"   2. 首次使用可能需要手动处理验证码")
    print(f"   3. 爬虫会自动下载和配置ChromeDriver")
    print(f"   4. 建议在网络良好的环境下运行")


if __name__ == "__main__":
    主函数()
