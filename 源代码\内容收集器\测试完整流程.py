# -*- coding: utf-8 -*-
"""
测试完整流程

作者: AI助手
日期: 2025-07-29
功能: 测试从采集到发布的完整流程
"""

import os
import sys
from datetime import datetime

# 添加当前目录到路径
当前目录 = os.path.dirname(os.path.abspath(__file__))
sys.path.append(当前目录)

# 导入各个模块
from 套利信息提取器 import 套利信息提取器
from 公众号自动发布器 import 公众号发布器


def 测试完整流程():
    """测试完整的套利分析和发布流程"""
    print("🧪 测试完整套利分析流程")
    print("=" * 50)
    
    # 1. 检查已采集的文章
    根目录 = os.path.dirname(os.path.dirname(当前目录))
    文章目录 = os.path.join(根目录, "采集结果", "微信文章")
    
    print(f"📁 检查文章目录: {文章目录}")
    
    if not os.path.exists(文章目录):
        print("❌ 文章目录不存在，请先运行采集器")
        return False
    
    文章文件 = [f for f in os.listdir(文章目录) if f.endswith('.txt')]
    print(f"📄 发现 {len(文章文件)} 篇文章")
    
    if not 文章文件:
        print("❌ 没有找到文章文件，请先采集一些文章")
        return False
    
    # 2. 执行套利分析
    print("\n🔍 开始套利信息分析...")
    分析器 = 套利信息提取器()
    
    套利信息列表 = 分析器.批量提取(文章目录)
    
    if not 套利信息列表:
        print("❌ 分析结果为空")
        return False
    
    print(f"✅ 分析完成，提取了 {len(套利信息列表)} 篇文章的套利信息")
    
    # 3. 生成分析报告
    print("\n📊 生成分析报告...")
    报告内容 = 分析器.生成套利报告(套利信息列表)
    
    # 保存报告
    采集结果目录 = os.path.join(根目录, "采集结果")
    报告文件 = os.path.join(采集结果目录, f"测试套利报告_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt")
    
    with open(报告文件, 'w', encoding='utf-8') as f:
        f.write(报告内容)
    
    print(f"💾 报告已保存: {os.path.basename(报告文件)}")
    
    # 4. 显示报告摘要
    print("\n📋 报告摘要:")
    print("-" * 30)
    
    lines = 报告内容.split('\n')
    for i, line in enumerate(lines[:20]):  # 显示前20行
        if line.strip():
            print(line)
    
    if len(lines) > 20:
        print("...")
        print(f"(完整报告共 {len(lines)} 行)")
    
    # 5. 测试公众号发布（如果配置了的话）
    print("\n📢 测试公众号发布功能...")
    
    发布器 = 公众号发布器()
    
    # 检查是否配置了公众号
    if hasattr(发布器, 'app_id') and 发布器.app_id and 发布器.app_id != "你的公众号AppID":
        print("✅ 检测到公众号配置")
        
        确认 = input("是否要测试发布到公众号？(y/n): ").strip().lower()
        
        if 确认 == 'y':
            print("🚀 开始测试发布...")
            
            # 创建测试内容（使用报告的前几段）
            测试内容 = "\n".join(lines[:30]) + "\n\n⚠️ 这是测试发布，完整报告请查看系统文件。"
            
            标题 = f"📊 套利分析测试 - {datetime.now().strftime('%H:%M')}"
            
            media_id = 发布器.创建图文素材(标题, 测试内容)
            
            if media_id:
                发布成功 = 发布器.发布图文消息(media_id)
                
                if 发布成功:
                    print("🎉 测试发布成功！请检查您的公众号")
                else:
                    print("❌ 发布失败")
            else:
                print("❌ 创建图文素材失败")
        else:
            print("⏭️ 跳过发布测试")
    else:
        print("⚠️ 未配置公众号AppID和AppSecret")
        print("💡 如需测试发布功能，请配置公众号信息")
        print(f"📝 配置文件: {os.path.join(根目录, '配置文件', '公众号发布配置.json')}")
    
    print("\n✅ 完整流程测试完成！")
    return True


def 显示配置指南():
    """显示公众号配置指南"""
    print("📖 公众号配置指南")
    print("=" * 50)
    print()
    print("🎯 要使用自动发布功能，您需要：")
    print()
    print("1️⃣ 微信公众号（服务号）")
    print("   • 需要已认证的服务号")
    print("   • 或者使用测试号（用于开发测试）")
    print()
    print("2️⃣ 获取AppID和AppSecret")
    print("   • 登录微信公众平台 (mp.weixin.qq.com)")
    print("   • 进入 开发 -> 基本配置")
    print("   • 复制AppID和AppSecret")
    print()
    print("3️⃣ 配置到系统中")
    根目录 = os.path.dirname(os.path.dirname(当前目录))
    配置文件路径 = os.path.join(根目录, "配置文件", "公众号发布配置.json")
    print(f"   • 编辑文件: {配置文件路径}")
    print("   • 将AppID和AppSecret填入对应位置")
    print()
    print("4️⃣ 测试发布")
    print("   • 运行本程序测试发布功能")
    print("   • 确认公众号收到测试消息")
    print()
    print("⚠️ 注意事项：")
    print("   • AppID和AppSecret需要保密")
    print("   • 测试号有功能限制")
    print("   • 正式发布需要服务号")


def 主函数():
    """主函数"""
    print("🧪 套利系统完整流程测试")
    print("=" * 60)
    
    while True:
        print("\n请选择操作:")
        print("1. 测试完整流程（采集→分析→发布）")
        print("2. 查看公众号配置指南")
        print("3. 查看当前系统状态")
        print("4. 退出")
        
        选择 = input("\n请输入选择 (1-4): ").strip()
        
        if 选择 == "1":
            测试完整流程()
            
        elif 选择 == "2":
            显示配置指南()
            
        elif 选择 == "3":
            # 显示系统状态
            根目录 = os.path.dirname(os.path.dirname(当前目录))
            
            print("\n📊 当前系统状态")
            print("=" * 30)
            
            # 检查文章
            文章目录 = os.path.join(根目录, "采集结果", "微信文章")
            if os.path.exists(文章目录):
                文章数量 = len([f for f in os.listdir(文章目录) if f.endswith('.txt')])
                print(f"📄 已采集文章: {文章数量} 篇")
            else:
                print("📄 已采集文章: 0 篇")
            
            # 检查报告
            采集结果目录 = os.path.join(根目录, "采集结果")
            if os.path.exists(采集结果目录):
                报告文件 = [f for f in os.listdir(采集结果目录) if f.startswith("套利分析报告_")]
                print(f"📊 分析报告: {len(报告文件)} 份")
            else:
                print("📊 分析报告: 0 份")
            
            # 检查配置
            配置文件路径 = os.path.join(根目录, "配置文件", "公众号发布配置.json")
            if os.path.exists(配置文件路径):
                print("⚙️ 公众号配置: ✅ 文件存在")
                
                try:
                    import json
                    with open(配置文件路径, 'r', encoding='utf-8') as f:
                        配置 = json.load(f)
                    
                    if 配置.get('app_id') != "你的公众号AppID":
                        print("🔑 AppID配置: ✅ 已配置")
                    else:
                        print("🔑 AppID配置: ❌ 未配置")
                except:
                    print("🔑 配置状态: ❌ 读取失败")
            else:
                print("⚙️ 公众号配置: ❌ 文件不存在")
            
        elif 选择 == "4":
            print("👋 测试结束，感谢使用！")
            break
            
        else:
            print("❌ 无效选择，请重新输入")


if __name__ == "__main__":
    主函数()
